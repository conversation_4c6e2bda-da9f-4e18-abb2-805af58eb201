{"name": "@morphik/mcp", "version": "1.0.11", "type": "module", "main": "build/index_http.js", "bin": {"morphik-mcp": "build/index_http.js"}, "scripts": {"test": "jest", "test:manual": "node --experimental-specifier-resolution=node --loader ts-node/esm src/test.ts", "build": "tsc && chmod 755 build/index.js", "build:streamable": "tsc && chmod 755 build/index_http.js", "start": "node build/index.js", "prepare": "npm run build", "prepublishOnly": "echo 'Leggo.'"}, "files": ["build"], "keywords": ["morphik", "mcp", "multimodal", "database", "retrieval", "claude", "vector-database", "model-context-protocol"], "author": "", "license": "ISC", "description": "MCP server for Morphik multimodal database", "repository": {"type": "git", "url": "git+https://github.com/morphik-org/morphik-npm-mcp.git"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/minimatch": "^5.1.2", "@types/sharp": "^0.32.0", "cors": "^2.8.5", "express": "^5.1.0", "form-data": "^4.0.2", "minimatch": "^10.0.1", "node-fetch": "^3.3.2", "sharp": "^0.32.6", "zod": "^3.24.2"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^22.13.14", "jest": "^29.7.0", "node-fetch": "^3.3.2", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}